| 序号 | 算子名称                | 算子类型               | 应用场景                                                     | 支持数据类型          | 支持芯片   | 优化的评判标准                                         | 预期目标/价值                         | 备注                                                     |
| ---- | ----------------------- | ---------------------- | ------------------------------------------------------------ | --------------------- | ---------- | ------------------------------------------------------ | ------------------------------------- | -------------------------------------------------------- |
| 1    | SageAttention           | 混合算子（较高难度）   | LLM模型、文生图模型、视频生成模型                            | 输入：FP16 输出：FP16 | 910B、910C | 性能提升2+倍，模型端到端精度损失<1%                    | 实现低精度模型量化、加速attention计算 | 本表格为揭榜挂帅华为赛题算子示例，仅供参考，非唯一标准。 |
| 2    | FlashAttention          | 混合算子（较高难度）   | 高效注意力计算                                               | FP16, BF16            | 910B、910C | 减少中间显存占用，优化IO带宽                           | 长序列处理速度提升5倍                 |                                                          |
| 3    | RotaryPositionEmbedding | 混合算子（较高难度）   | 位置编码注入                                                 | FP16, BF16            | 910B、910C | 向量化旋转计算，避免显式拼接                           | 位置编码零显存增长，端侧友好          |                                                          |
| 4    | BatchNorm (批量归一化)  | 混合算子（较高难度）   | 用于加速训练过程，减少内部协变量偏移                         | FP32, FP16            | 910B、910C | 性能提升幅度、支持的输入/输出 Tensor 的 shape 覆盖范围 | 提高模型训练和推理的稳定性            |                                                          |
| 5    | MatMul (矩阵乘法)       | 混合算子（较高难度）   | 全连接层、注意力QKV投影，广泛应用于神经网络中的全连接层、注意力机制等 | FP16, BF16, INT8      | 910B、910C | 计算密度优化，内存访问效率提升                         | 加速矩阵运算，降低30%推理延迟         |                                                          |
| 6    | Conv2D (二维卷积)       | Cube算子（中难度）     | 图像处理、计算机视觉任务中的卷积层                           | FP32, FP16, INT8      | 910B、910C | 性能提升幅度、支持的输入/输出 Tensor 的 shape 覆盖范围 | 加速图像处理任务，提高模型推理效率    |                                                          |
| 7    | LayerNorm               | Cube算子（中难度）     | 残差连接后归一化                                             | FP16, BF16            | 910B、910C | 融合乘加操作，减少显存读写                             | 端侧部署时内存占用降低40%             |                                                          |
| 8    | Softmax (软最大值函数)  | vector算子（较低难度） | 注意力权重归一化                                             | FP16, BF16, FP32      | 910B、910C | 分块计算避免数值溢出，并行化优化                       | 支持超大维度（如16384）稳定计算       |                                                          |
| 9    | GeLU                    | vector算子（较低难度） | FFN层激活函数                                                | FP16, BF16            | 910B、910C | 近似计算加速，融合前置LayerNorm                        | 算子融合减少20% kernel调用            |                                                          |
| 10   | TopK                    | vector算子（较低难度） | 生成式采样输出选择                                           | FP16, INT32           | 910B、910C | 高效排序算法，支持动态K值                              | 确保<answer>输出格式实时性            |                                                          |
| 11   | Gather                  | vector算子（较低难度） | 词表查询、MOE路由选择                                        | FP16, INT32           | 910B、910C | 内存连续性访问优化                                     | 降低稀疏操作延迟50%                   |                                                          |
| 12   | FusedAddBias            | vector算子（较低难度） | 残差连接+偏置融合                                            | FP16, BF16            | 910B、910C | 合并访存操作，减少中间结果                             | 算子融合提升10%计算密度               |                                                          |
| 13   | Slice/Concat            | vector算子（较低难度） | 动态序列裁剪/拼接（如思维链生成）                            | FP16, INT32           | 910B、910C | 零拷贝内存管理，支持非规则shape                        | 满足<think>过程动态长度需求           |                                                          |
| 14   | ReLU (线性整流函数)     | vector算子（较低难度） | 激活函数，广泛应用于各种神经网络层                           | FP32, FP16, INT8      | 910B、910C | 性能提升幅度、支持的输入/输出 Tensor 的 shape 覆盖范围 | 加速激活函数计算，提高模型推理速度    |                                                          |
| 15   | Pooling (池化)          | vector算子（较低难度） | 用于减少特征图的尺寸，提取重要特征                           | FP32, FP16, INT8      | 910B、910C | 性能提升幅度、支持的输入/输出 Tensor 的 shape 覆盖范围 | 加速特征提取，减少计算资源消耗        |                                                          |
| 16   | Transpose (转置)        | vector算子（较低难度） | 用于改变张量的维度顺序                                       | FP32, FP16, INT8      | 910B、910C | 性能提升幅度、支持的输入/输出 Tensor 的 shape 覆盖范围 | 提高数据预处理和后处理的效率          |                                                          |