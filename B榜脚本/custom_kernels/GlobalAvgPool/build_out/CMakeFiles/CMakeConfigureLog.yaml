
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Linux - 4.19.90-vhulk2211.3.0.h1543.eulerosv2r10.aarch64 - aarch64
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/4.0.3/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/4.0.3/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/work/CANN-Op-Tuner/B\u699c\u811a\u672c/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-rcFA95"
      binary: "/home/<USER>/work/CANN-Op-Tuner/B\u699c\u811a\u672c/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-rcFA95"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-rcFA95'
        
        Run Build Command(s): /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_cc498/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_cc498.dir/build.make CMakeFiles/cmTC_cc498.dir/build
        gmake[1]: Entering directory '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-rcFA95'
        Building C object CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -o CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -c /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        Target: aarch64-linux-gnu
        Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64
        Thread model: posix
        gcc version 7.3.0 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64'
         /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/cc1 -quiet -v /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -version -o /tmp/ccUqSu4a.s
        GNU C11 (GCC) version 7.3.0 (aarch64-linux-gnu)
        	compiled by GNU C version 7.3.0, GMP version 6.1.2, MPFR version 3.1.6-p2, MPC version 1.1.0, isl version none
        warning: GMP header version 6.1.2 differs from library version 6.2.0.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../aarch64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-linux-gnu/7.3.0/include
         /usr/local/include
         /usr/include
        End of search list.
        GNU C11 (GCC) version 7.3.0 (aarch64-linux-gnu)
        	compiled by GNU C version 7.3.0, GMP version 6.1.2, MPFR version 3.1.6-p2, MPC version 1.1.0, isl version none
        warning: GMP header version 6.1.2 differs from library version 6.2.0.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 1433d69624ea98ab477c9952db411a52
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64'
         as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o /tmp/ccUqSu4a.s
        GNU assembler version 2.34 (aarch64-openEuler-linux) using BFD version (GNU Binutils) 2.34
        COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64'
        Linking C executable cmTC_cc498
        /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cc498.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper
        Target: aarch64-linux-gnu
        Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64
        Thread model: posix
        gcc version 7.3.0 (GCC) 
        COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_cc498' '-mlittle-endian' '-mabi=lp64'
         /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/collect2 -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5EI00b.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_cc498 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o
        collect2 version 7.3.0
        /usr/bin/ld -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5EI00b.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_cc498 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o
        GNU ld (GNU Binutils) 2.34
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_cc498' '-mlittle-endian' '-mabi=lp64'
        /usr/bin/cc  -v -Wl,-v CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -o cmTC_cc498
        gmake[1]: Leaving directory '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-rcFA95'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include]
          add: [/usr/local/include]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include] ==> [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include;/usr/local/include;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-rcFA95']
        ignore line: []
        ignore line: [Run Build Command(s): /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_cc498/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_cc498.dir/build.make CMakeFiles/cmTC_cc498.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-rcFA95']
        ignore line: [Building C object CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -c /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 7.3.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64']
        ignore line: [ /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/cc1 -quiet -v /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -version -o /tmp/ccUqSu4a.s]
        ignore line: [GNU C11 (GCC) version 7.3.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 7.3.0  GMP version 6.1.2  MPFR version 3.1.6-p2  MPC version 1.1.0  isl version none]
        ignore line: [warning: GMP header version 6.1.2 differs from library version 6.2.0.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../aarch64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/7.3.0/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C11 (GCC) version 7.3.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 7.3.0  GMP version 6.1.2  MPFR version 3.1.6-p2  MPC version 1.1.0  isl version none]
        ignore line: [warning: GMP header version 6.1.2 differs from library version 6.2.0.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 1433d69624ea98ab477c9952db411a52]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64']
        ignore line: [ as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o /tmp/ccUqSu4a.s]
        ignore line: [GNU assembler version 2.34 (aarch64-openEuler-linux) using BFD version (GNU Binutils) 2.34]
        ignore line: [COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64']
        ignore line: [Linking C executable cmTC_cc498]
        ignore line: [/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cc498.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 7.3.0 (GCC) ]
        ignore line: [COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_cc498' '-mlittle-endian' '-mabi=lp64']
        link line: [ /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/collect2 -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5EI00b.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_cc498 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o]
          arg [/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cc5EI00b.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_cc498] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/7.3.0] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../..] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--no-as-needed] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--no-as-needed] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o]
        ignore line: [collect2 version 7.3.0]
        ignore line: [/usr/bin/ld -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/cc5EI00b.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_cc498 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_cc498.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o]
        linker tool for 'C': /usr/bin/ld
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o] ==> [/usr/lib64/crt1.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o] ==> [/usr/lib64/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o] ==> [/usr/lib64/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0] ==> [/usr/lib/gcc/aarch64-linux-gnu/7.3.0]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64] ==> [/usr/lib64]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../..] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/usr/lib64/crt1.o;/usr/lib64/crti.o;/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o;/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o;/usr/lib64/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0;/usr/lib64;/lib64;/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils) 2.34
  -
    kind: "try_compile-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/work/CANN-Op-Tuner/B\u699c\u811a\u672c/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-h8xPz7"
      binary: "/home/<USER>/work/CANN-Op-Tuner/B\u699c\u811a\u672c/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-h8xPz7"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-h8xPz7'
        
        Run Build Command(s): /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_64d65/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_64d65.dir/build.make CMakeFiles/cmTC_64d65.dir/build
        gmake[1]: Entering directory '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-h8xPz7'
        Building CXX object CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -c /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        Target: aarch64-linux-gnu
        Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64
        Thread model: posix
        gcc version 7.3.0 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
         /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/cc1plus -quiet -v -D_GNU_SOURCE /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -version -o /tmp/ccGAe83c.s
        GNU C++14 (GCC) version 7.3.0 (aarch64-linux-gnu)
        	compiled by GNU C version 7.3.0, GMP version 6.1.2, MPFR version 3.1.6-p2, MPC version 1.1.0, isl version none
        warning: GMP header version 6.1.2 differs from library version 6.2.0.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../aarch64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0
         /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/aarch64-linux-gnu
         /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/backward
         /usr/lib/gcc/aarch64-linux-gnu/7.3.0/include
         /usr/local/include
         /usr/include
        End of search list.
        GNU C++14 (GCC) version 7.3.0 (aarch64-linux-gnu)
        	compiled by GNU C version 7.3.0, GMP version 6.1.2, MPFR version 3.1.6-p2, MPC version 1.1.0, isl version none
        warning: GMP header version 6.1.2 differs from library version 6.2.0.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 44b23371289989dfb7d47684a3f17ece
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
         as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccGAe83c.s
        GNU assembler version 2.34 (aarch64-openEuler-linux) using BFD version (GNU Binutils) 2.34
        COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
        Linking CXX executable cmTC_64d65
        /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E cmake_link_script CMakeFiles/cmTC_64d65.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper
        Target: aarch64-linux-gnu
        Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64
        Thread model: posix
        gcc version 7.3.0 (GCC) 
        COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_64d65' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
         /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/collect2 -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccphesvl.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_64d65 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o
        collect2 version 7.3.0
        /usr/bin/ld -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccphesvl.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_64d65 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o
        GNU ld (GNU Binutils) 2.34
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_64d65' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
        /usr/bin/c++  -v -Wl,-v CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_64d65
        gmake[1]: Leaving directory '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-h8xPz7'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0]
          add: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/aarch64-linux-gnu]
          add: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/backward]
          add: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include]
          add: [/usr/local/include]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0] ==> [/usr/include/c++/7.3.0]
        collapse include dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/aarch64-linux-gnu] ==> [/usr/include/c++/7.3.0/aarch64-linux-gnu]
        collapse include dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/backward] ==> [/usr/include/c++/7.3.0/backward]
        collapse include dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include] ==> [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/7.3.0;/usr/include/c++/7.3.0/aarch64-linux-gnu;/usr/include/c++/7.3.0/backward;/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include;/usr/local/include;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-h8xPz7']
        ignore line: []
        ignore line: [Run Build Command(s): /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_64d65/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_64d65.dir/build.make CMakeFiles/cmTC_64d65.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels/GlobalAvgPool/build_out/CMakeFiles/CMakeScratch/TryCompile-h8xPz7']
        ignore line: [Building CXX object CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -c /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 7.3.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
        ignore line: [ /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/cc1plus -quiet -v -D_GNU_SOURCE /home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -version -o /tmp/ccGAe83c.s]
        ignore line: [GNU C++14 (GCC) version 7.3.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 7.3.0  GMP version 6.1.2  MPFR version 3.1.6-p2  MPC version 1.1.0  isl version none]
        ignore line: [warning: GMP header version 6.1.2 differs from library version 6.2.0.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../aarch64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0]
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/aarch64-linux-gnu]
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../include/c++/7.3.0/backward]
        ignore line: [ /usr/lib/gcc/aarch64-linux-gnu/7.3.0/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (GCC) version 7.3.0 (aarch64-linux-gnu)]
        ignore line: [	compiled by GNU C version 7.3.0  GMP version 6.1.2  MPFR version 3.1.6-p2  MPC version 1.1.0  isl version none]
        ignore line: [warning: GMP header version 6.1.2 differs from library version 6.2.0.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 44b23371289989dfb7d47684a3f17ece]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
        ignore line: [ as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccGAe83c.s]
        ignore line: [GNU assembler version 2.34 (aarch64-openEuler-linux) using BFD version (GNU Binutils) 2.34]
        ignore line: [COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
        ignore line: [Linking CXX executable cmTC_64d65]
        ignore line: [/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/bin/cmake -E cmake_link_script CMakeFiles/cmTC_64d65.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper]
        ignore line: [Target: aarch64-linux-gnu]
        ignore line: [Configured with: ../configure --prefix=/usr --mandir=/usr/share/man --infodir=/usr/share/info --enable-shared --enable-threads=posix --enable-checking=release --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-linker-hash-style=gnu --enable-languages=c,c++,objc,obj-c++,fortran,lto --enable-plugin --enable-initfini-array --disable-libgcj --without-isl --without-cloog --enable-gnu-indirect-function --build=aarch64-linux-gnu --with-stage1-ldflags=' -Wl,-z,relro,-z,now' --with-boot-ldflags=' -Wl,-z,relro,-z,now' --with-multilib-list=lp64]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 7.3.0 (GCC) ]
        ignore line: [COMPILER_PATH=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/:/usr/libexec/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/7.3.0/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_64d65' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
        link line: [ /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/collect2 -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccphesvl.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_64d65 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o]
          arg [/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccphesvl.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib/ld-linux-aarch64.so.1] ==> ignore
          arg [-X] ==> ignore
          arg [-EL] ==> ignore
          arg [-maarch64linux] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_64d65] ==> ignore
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/7.3.0] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../..] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o]
          arg [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o] ==> obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o]
        ignore line: [collect2 version 7.3.0]
        ignore line: [/usr/bin/ld -plugin /usr/libexec/gcc/aarch64-linux-gnu/7.3.0/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/aarch64-linux-gnu/7.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccphesvl.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_64d65 /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../.. -v CMakeFiles/cmTC_64d65.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o /usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o]
        linker tool for 'CXX': /usr/bin/ld
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crt1.o] ==> [/usr/lib64/crt1.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crti.o] ==> [/usr/lib64/crti.o]
        collapse obj [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64/crtn.o] ==> [/usr/lib64/crtn.o]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0] ==> [/usr/lib/gcc/aarch64-linux-gnu/7.3.0]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../../../lib64] ==> [/usr/lib64]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/7.3.0/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib64/crt1.o;/usr/lib64/crti.o;/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtbegin.o;/usr/lib/gcc/aarch64-linux-gnu/7.3.0/crtend.o;/usr/lib64/crtn.o]
        implicit dirs: [/usr/lib/gcc/aarch64-linux-gnu/7.3.0;/usr/lib64;/lib64;/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/home/<USER>/anaconda3/envs/python-3.9.10/lib/python3.9/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils) 2.34
...
