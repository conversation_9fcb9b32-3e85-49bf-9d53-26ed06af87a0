#!/usr/bin/env python3
"""
华为挑战杯B榜 - v3.1算子修复验证测试
测试自定义算子是否真正被使用

使用方法:
python test_operators_v31.py
"""

import os
import sys
import time
import torch
import torch_npu  # type: ignore
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入修复后的模块
sys.path.insert(0, str(Path(__file__).parent))
from competition_model_optimized import op_registry, initialize_challenge_cup_operators


def test_monkey_patching():
    """测试monkey patching是否生效"""
    print("🔧 测试Monkey Patching机制")
    print("=" * 50)
    
    # 检查是否有原始函数的备份
    has_gelu_backup = hasattr(torch.nn.functional, '_original_gelu')
    has_layernorm_backup = hasattr(torch.nn.functional, '_original_layer_norm')
    has_matmul_backup = hasattr(torch, '_original_matmul')
    
    print(f"  - GELU备份: {'✅ 存在' if has_gelu_backup else '❌ 不存在'}")
    print(f"  - LayerNorm备份: {'✅ 存在' if has_layernorm_backup else '❌ 不存在'}")
    print(f"  - MatMul备份: {'✅ 存在' if has_matmul_backup else '❌ 不存在'}")
    
    return has_gelu_backup or has_layernorm_backup or has_matmul_backup


def test_operator_calls():
    """测试算子调用是否使用自定义实现"""
    print("\n⚡ 测试算子调用效果")
    print("=" * 50)
    
    device = 'npu:0'
    
    # 获取调用前的统计
    pre_stats = op_registry.get_performance_report()
    pre_custom_calls = sum(stats.get('custom_calls', 0) for stats in pre_stats['performance'].values())
    
    print(f"调用前自定义算子使用次数: {pre_custom_calls}")
    
    # 测试GELU
    print("\n🧪 测试GELU算子:")
    try:
        test_tensor = torch.randn(4, 8, device=device, dtype=torch.float16)
        start_time = time.time()
        result = torch.nn.functional.gelu(test_tensor)  # 这应该触发我们的monkey patch
        exec_time = (time.time() - start_time) * 1000
        print(f"  ✅ GELU执行成功 - 耗时: {exec_time:.2f}ms")
        print(f"  📊 输出形状: {result.shape}")
    except Exception as e:
        print(f"  ❌ GELU执行失败: {e}")
    
    # 测试LayerNorm
    print("\n🧪 测试LayerNorm算子:")
    try:
        test_tensor = torch.randn(4, 8, device=device, dtype=torch.float16)
        weight = torch.ones(8, device=device, dtype=torch.float16)
        bias = torch.zeros(8, device=device, dtype=torch.float16)
        start_time = time.time()
        result = torch.nn.functional.layer_norm(test_tensor, [8], weight, bias)
        exec_time = (time.time() - start_time) * 1000
        print(f"  ✅ LayerNorm执行成功 - 耗时: {exec_time:.2f}ms")
        print(f"  📊 输出形状: {result.shape}")
    except Exception as e:
        print(f"  ❌ LayerNorm执行失败: {e}")
    
    # 测试MatMul
    print("\n🧪 测试MatMul算子:")
    try:
        a = torch.randn(4, 8, device=device, dtype=torch.float16)
        b = torch.randn(8, 4, device=device, dtype=torch.float16)
        start_time = time.time()
        result = torch.matmul(a, b)
        exec_time = (time.time() - start_time) * 1000
        print(f"  ✅ MatMul执行成功 - 耗时: {exec_time:.2f}ms")
        print(f"  📊 输出形状: {result.shape}")
    except Exception as e:
        print(f"  ❌ MatMul执行失败: {e}")
    
    # 获取调用后的统计
    post_stats = op_registry.get_performance_report()
    post_custom_calls = sum(stats.get('custom_calls', 0) for stats in post_stats['performance'].values())
    
    custom_calls_delta = post_custom_calls - pre_custom_calls
    print(f"\n📊 调用后自定义算子使用次数: {post_custom_calls}")
    print(f"📈 本次测试新增自定义算子调用: {custom_calls_delta} 次")
    
    return custom_calls_delta > 0


def test_performance_comparison():
    """性能对比测试"""
    print("\n🚀 性能对比测试")
    print("=" * 50)
    
    device = 'npu:0'
    test_tensor = torch.randn(32, 128, device=device, dtype=torch.float16)
    iterations = 50
    
    # 测试标准GELU性能
    if hasattr(torch.nn.functional, '_original_gelu'):
        print("📊 测试标准GELU性能:")
        start_time = time.time()
        for _ in range(iterations):
            torch.nn.functional._original_gelu(test_tensor)
        torch_npu.npu.synchronize()  # type: ignore
        standard_time = (time.time() - start_time) * 1000
        print(f"  标准GELU: {standard_time:.2f}ms ({iterations} 次)")
    else:
        standard_time = 0
        print("  ⚠️ 未找到标准GELU备份")
    
    # 测试自定义GELU性能
    print("📊 测试自定义GELU性能:")
    start_time = time.time()
    for _ in range(iterations):
        torch.nn.functional.gelu(test_tensor)  # 使用我们的monkey patch版本
    torch_npu.npu.synchronize()  # type: ignore
    custom_time = (time.time() - start_time) * 1000
    print(f"  自定义GELU: {custom_time:.2f}ms ({iterations} 次)")
    
    if standard_time > 0 and custom_time > 0:
        if custom_time < standard_time:
            speedup = standard_time / custom_time
            print(f"  🚀 性能提升: {speedup:.2f}x (更快)")
        else:
            slowdown = custom_time / standard_time
            print(f"  🐌 性能下降: {slowdown:.2f}x (更慢)")
    
    return custom_time


def test_vllm_integration():
    """测试vLLM集成环境"""
    print("\n🎯 测试vLLM集成环境")
    print("=" * 50)
    
    # 检查环境变量
    env_vars = [
        'VLLM_USE_CUSTOM_OPS',
        'VLLM_USE_NPU_KERNELS',
        'TORCH_NPU_ENABLE_FUSION',
        'TORCH_SHOW_CPP_STACKTRACES'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, '未设置')
        print(f"  - {var}: {value}")
    
    # 检查算子注册情况
    perf_report = op_registry.get_performance_report()
    print(f"\n📋 算子注册状态:")
    print(f"  - 已注册算子: {perf_report['total_registered']} 个")
    print(f"  - 融合优化: {'✅ 已启用' if perf_report['fusion_enabled'] else '❌ 未启用'}")
    print(f"  - Monkey补丁: {'✅ 已应用' if perf_report['monkey_patches_applied'] else '❌ 未应用'}")
    print(f"  - 替换算子: {list(perf_report['torch_ops_replaced'].keys())}")
    
    return perf_report['fusion_enabled']


def test_competition_class():
    """简单测试Competition类"""
    print("\n🏆 测试Competition类集成")
    print("=" * 50)
    
    try:
        from competition_model_optimized import Competition
        
        print("正在初始化Competition类...")
        competition = Competition()
        
        # 获取性能统计
        perf_stats = competition.get_performance_stats()
        
        print(f"✅ Competition类初始化成功")
        print(f"  - 算子融合: {'已启用' if perf_stats['fusion_enabled'] else '未启用'}")
        print(f"  - 版本: {perf_stats.get('version', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Competition类测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 华为挑战杯B榜v3.1算子修复验证测试")
    print("🔧 测试自定义算子是否真正被使用")
    print("=" * 60)
    
    # 初始化算子系统
    print("🚀 初始化算子系统...")
    success = initialize_challenge_cup_operators()
    
    if not success:
        print("❌ 算子系统初始化失败")
        return
    
    # 运行测试
    test_results = {}
    
    try:
        # 测试1：Monkey Patching
        test_results['monkey_patching'] = test_monkey_patching()
        
        # 测试2：算子调用
        test_results['operator_calls'] = test_operator_calls()
        
        # 测试3：性能对比
        test_results['performance'] = test_performance_comparison()
        
        # 测试4：vLLM集成
        test_results['vllm_integration'] = test_vllm_integration()
        
        # 测试5：Competition类
        test_results['competition_class'] = test_competition_class()
        
        # 输出总结
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"  - Monkey Patching: {'✅ 通过' if test_results['monkey_patching'] else '❌ 失败'}")
        print(f"  - 算子调用验证: {'✅ 通过' if test_results['operator_calls'] else '❌ 失败'}")
        print(f"  - 性能测试: {'✅ 完成' if test_results['performance'] > 0 else '❌ 失败'}")
        print(f"  - vLLM集成: {'✅ 通过' if test_results['vllm_integration'] else '❌ 失败'}")
        print(f"  - Competition类: {'✅ 通过' if test_results['competition_class'] else '❌ 失败'}")
        
        success_count = sum(1 for result in test_results.values() if result)
        total_count = len(test_results)
        success_rate = success_count / total_count * 100
        
        print(f"\n🎯 总体成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("🎉 v3.1修复效果良好！自定义算子应该能正常工作")
        elif success_rate >= 60:
            print("⚠️ v3.1修复部分生效，可能需要进一步调试")
        else:
            print("❌ v3.1修复效果不佳，建议检查算子安装")
        
        # 输出详细的算子统计
        final_report = op_registry.get_performance_report()
        if final_report['performance']:
            print("\n🔧 算子使用详情:")
            for op_name, stats in final_report['performance'].items():
                total = stats['total_calls']
                custom = stats['custom_calls']
                if total > 0:
                    rate = custom / total * 100
                    print(f"  - {op_name}: {custom}/{total} ({rate:.1f}% 使用自定义)")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 